package corp.jamaro.jamaroescritoriofx.appfx.util;

import javafx.application.Platform;
import javafx.scene.control.ProgressIndicator;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Consumer;

/**
 * Clase de utilidad para suscribirse a operaciones reactivos (Mono o Flux)
 * mostrando un indicador de carga mientras se procesa la información.
 */
@Slf4j
@UtilityClass
public class LoadingUtil {

    /**
     * Suscribe a un Mono mostrando el indicador de carga durante la operación.
     *
     * @param indicator El ProgressIndicator que se mostrará en la UI.
     * @param operation El Mono que representa la operación asíncrona.
     * @param onSuccess Acción a ejecutar cuando la operación es exitosa.
     * @param onError   Acción a ejecutar cuando ocurre un error.
     * @param <T>       Tipo de dato emitido por el Mono.
     * @return Disposable para la suscripción.
     */
    public static <T> Disposable subscribeWithLoading(ProgressIndicator indicator,
                                                      Mono<T> operation,
                                                      Consumer<T> onSuccess,
                                                      Consumer<Throwable> onError) {
        // Mostrar el indicador en el hilo de JavaFX
        Platform.runLater(() -> {
            log.debug("Mostrando indicador de carga para Mono.");
            indicator.setVisible(true);
        });

        // Retornamos el Disposable que crea .subscribe(...)
        return operation
                .doFinally(signalType -> Platform.runLater(() -> {
                    log.debug("Ocultando indicador de carga para Mono (signal: {}).", signalType);
                    indicator.setVisible(false);
                }))
                .subscribe(
                        result -> {
                            log.debug("Operación Mono completada con éxito.");
                            onSuccess.accept(result);
                        },
                        error -> {
                            log.error("Error durante la operación Mono.", error);
                            onError.accept(error);
                        }
                );
    }

    /**
     * Suscribe a un Flux mostrando el indicador de carga durante la operación.
     * En esta versión se oculta el indicador tan pronto se reciba el primer elemento.
     *
     * @param indicator  El ProgressIndicator que se mostrará en la UI.
     * @param operation  El Flux que representa la operación asíncrona.
     * @param onNext     Acción a ejecutar para cada elemento emitido.
     * @param onError    Acción a ejecutar cuando ocurre un error.
     * @param onComplete Acción a ejecutar al finalizar el flujo.
     * @param <T>        Tipo de dato emitido por el Flux.
     * @return Disposable para la suscripción.
     */
    public static <T> Disposable subscribeWithLoading(ProgressIndicator indicator,
                                                      Flux<T> operation,
                                                      Consumer<T> onNext,
                                                      Consumer<Throwable> onError,
                                                      Runnable onComplete) {
        // Mostrar el indicador en el hilo de JavaFX
        Platform.runLater(() -> {
            log.debug("Mostrando indicador de carga para Flux.");
            indicator.setVisible(true);
        });

        // Usamos un AtomicBoolean para asegurar que el indicador se oculte sólo una vez
        AtomicBoolean firstElementReceived = new AtomicBoolean(false);

        // Retornamos el Disposable que crea .subscribe(...)
        return operation
                .doFinally(signalType -> {
                    // Si el flujo termina sin emitir ningún elemento, ocultamos el indicador
                    if (firstElementReceived.compareAndSet(false, true)) {
                        Platform.runLater(() -> {
                            log.debug("Ocultando indicador de carga para Flux en doFinally (sin elementos, signal: {}).", signalType);
                            indicator.setVisible(false);
                        });
                    }
                })
                .subscribe(
                        item -> {
                            // Ocultar el indicador en el primer elemento recibido
                            if (firstElementReceived.compareAndSet(false, true)) {
                                Platform.runLater(() -> {
                                    log.debug("Ocultando indicador de carga para Flux en onNext.");
                                    indicator.setVisible(false);
                                });
                            }
                            log.debug("Emitiendo item en Flux: {}", item);
                            onNext.accept(item);
                        },
                        error -> {
                            log.error("Error durante la operación Flux.", error);
                            onError.accept(error);
                        },
                        () -> {
                            log.debug("Flujo Flux completado.");
                            onComplete.run();
                        }
                );
    }

    /**
     * Ejecuta una operación Mono usando blockOptional con manejo automático del progress indicator,
     * threading correcto y manejo de errores estándar. Este método es ideal para búsquedas que
     * pueden retornar Mono vacío (elemento no encontrado).
     *
     * @param indicator El ProgressIndicator que se mostrará durante la operación.
     * @param operation El Mono que representa la operación de búsqueda.
     * @param onSuccess Callback ejecutado en el hilo de UI cuando se obtiene un resultado (presente o vacío).
     * @param onError   Callback ejecutado en el hilo de UI cuando ocurre un error.
     * @param timeout   Timeout para la operación blockOptional.
     * @param <T>       Tipo de dato emitido por el Mono.
     * @return Disposable para la operación asíncrona.
     */
    public static <T> Disposable executeWithBlockOptional(ProgressIndicator indicator,
                                                          Mono<T> operation,
                                                          Consumer<Optional<T>> onSuccess,
                                                          Consumer<Throwable> onError,
                                                          Duration timeout) {
        // Mostrar indicador de carga en el hilo de UI
        Platform.runLater(() -> {
            log.debug("Mostrando indicador de carga para operación blockOptional.");
            indicator.setVisible(true);
        });

        // Ejecutar la operación en boundedElastic scheduler
        return Mono.fromCallable(() -> {
                    try {
                        // Aplicar el patrón estándar: onErrorResume + blockOptional
                        Optional<T> result = operation
                                .onErrorResume(error -> {
                                    log.debug("Error en operación, retornando Mono vacío: {}", error.getMessage());
                                    return Mono.empty();
                                })
                                .blockOptional(timeout);

                        return result;
                    } catch (RuntimeException e) {
                        if (e.getCause() instanceof InterruptedException) {
                            Thread.currentThread().interrupt();
                            log.debug("Operación interrumpida");
                            throw new RuntimeException("Operación interrumpida", e);
                        } else {
                            log.error("Error en operación: {}", e.getMessage(), e);
                            throw new RuntimeException("Error en la operación: " + e.getMessage(), e);
                        }
                    } catch (Exception e) {
                        log.error("Error inesperado en operación: {}", e.getMessage(), e);
                        throw new RuntimeException("Error inesperado: " + e.getMessage(), e);
                    }
                })
                .subscribeOn(Schedulers.boundedElastic())
                .subscribe(
                        result -> {
                            // Ocultar indicador y ejecutar callback en hilo de UI
                            Platform.runLater(() -> {
                                indicator.setVisible(false);
                                onSuccess.accept(result);
                            });
                        },
                        error -> {
                            // Ocultar indicador y ejecutar callback de error en hilo de UI
                            Platform.runLater(() -> {
                                indicator.setVisible(false);
                                onError.accept(error);
                            });
                        }
                );
    }

    /**
     * Sobrecarga con timeout por defecto de 10 segundos.
     */
    public static <T> Disposable executeWithBlockOptional(ProgressIndicator indicator,
                                                          Mono<T> operation,
                                                          Consumer<Optional<T>> onSuccess,
                                                          Consumer<Throwable> onError) {
        return executeWithBlockOptional(indicator, operation, onSuccess, onError, Duration.ofSeconds(10));
    }
}
