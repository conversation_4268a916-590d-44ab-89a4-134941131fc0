package corp.jamaro.jamaroescritoriofx.appfx.ventas.controller;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.model.FXMLEnum;
import corp.jamaro.jamaroescritoriofx.appfx.model.User;
import corp.jamaro.jamaroescritoriofx.appfx.service.GeneralService;
import corp.jamaro.jamaroescritoriofx.appfx.service.NavigationService;
import corp.jamaro.jamaroescritoriofx.appfx.service.SpringFXMLLoader;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import corp.jamaro.jamaroescritoriofx.appfx.util.LoadingUtil;
import corp.jamaro.jamaroescritoriofx.connection.dto.LoginResponse;
import corp.jamaro.jamaroescritoriofx.connection.security.SecurityContext;
import corp.jamaro.jamaroescritoriofx.connection.service.ConnectionService;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.dto.UniversalSaleGuiDto;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.service.gui.MainSaleGuiService;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.service.gui.UniversalSaleGuiService;
import javafx.animation.Timeline;
import javafx.animation.KeyFrame;
import javafx.application.Platform;
import javafx.event.EventHandler;
import javafx.fxml.FXML;
import javafx.scene.Parent;
import javafx.scene.control.*;
import javafx.scene.input.*;
import javafx.scene.Node;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import javafx.util.Duration;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import reactor.core.Disposable;

import java.net.URL;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class UnisersalSaleGuiController extends BaseController {

    // Inyección de dependencias
    private final UniversalSaleGuiService universalSaleGuiService;
    private final MainSaleGuiService mainSaleGuiService;
    private final GeneralService generalService;
    private final SecurityContext securityContext;
    private final SpringFXMLLoader springFXMLLoader;
    private final AlertUtil alertUtil;
    private final ConnectionService connectionService;
    private final NavigationService navigationService;

    @FXML
    private Label lblInfo;

    @FXML
    private ProgressIndicator loadingIndicator;

    @FXML
    private Menu mAuditar;

    @FXML
    private Menu mOpciones;

    @FXML
    private MenuBar mbUniversal;

    @FXML
    private MenuItem miLogOut;

    @FXML
    private StackPane spUniversal;

    // Tab principal (MainSaleGui del usuario actual) – no debe ser cerrable.
    @FXML
    private Tab tabPrincipal;

    // TabPane que contendrá el tab principal y los tabs de auditoría.
    @FXML
    private TabPane tpUniversal;

    // Mapa para relacionar cada MainSaleGuiId de auditoría con su Tab.
    private final Map<UUID, Tab> auditingTabs = new ConcurrentHashMap<>();

    // Referencia actual del UniversalSaleGuiDto que se actualiza con los datos del servidor.
    private UniversalSaleGuiDto currentUGuiDto;

    // Variables para el sistema de bloqueo por inactividad
    private static final double INACTIVITY_TIMEOUT_SECONDS = 18.0; // Configurable: tiempo de inactividad en segundos
    private Timeline inactivityTimeline;
    private VBox screenLockOverlay;
    private PasswordField rfidLockField;
    private boolean isScreenLocked = false;

    // Handler genérico para cualquier evento de entrada
    private EventHandler<InputEvent> globalActivityHandler;
    
    // Handler específico para eventos de teclado (CTRL+F4)
    private EventHandler<KeyEvent> keyEventHandler;

    /*
     * FLUJO DEL SISTEMA DE BLOQUEO POR INACTIVIDAD Y RFID:
     *
     * 1. BLOQUEO POR INACTIVIDAD:
     *    - Se detecta inactividad de mouse/teclado por INACTIVITY_TIMEOUT_SECONDS
     *    - Se muestra overlay de bloqueo con campo RFID
     *    - Se pausa el timer de inactividad mientras está bloqueado
     *
     * 2. DESBLOQUEO CON RFID:
     *    - Usuario ingresa RFID y presiona Enter
     *    - Se busca usuario por RFID (sin actualizar contexto de seguridad)
     *    - Si es el mismo usuario: se desbloquea la pantalla
     *    - Si es usuario diferente: se hace cambio optimizado de usuario
     *
     * 3. CAMBIO OPTIMIZADO DE USUARIO:
     *    - Se limpian suscripciones actuales (sin cerrar vista)
     *    - Se hace login completo con RFID (actualiza SecurityContext)
     *    - Se recargan datos para el nuevo usuario in-situ
     *    - Se desbloquea la pantalla con el nuevo usuario activo
     */

    @Override
    public void initialize(URL url, java.util.ResourceBundle resourceBundle) {
        // Permitir cierre de tabs en el TabPane (los tabs que se marquen como closable mostrarán su "X")
        tpUniversal.setTabClosingPolicy(TabPane.TabClosingPolicy.ALL_TABS);
        // Asegurarse de que el tab principal no tenga botón de cierre.
        tabPrincipal.setClosable(false);

        // Verificar autenticación
        if (!securityContext.isAuthenticated() || securityContext.getAuthenticatedUser() == null) {
            log.error("No hay usuario autenticado en SecurityContext. Se aborta la inicialización.");
            return;
        }

        updateLblInfo();

        // Suscribirse al flujo de cambios de UniversalSaleGuiDto
        UUID userId = securityContext.getAuthenticatedUser().getId();
        log.info("Suscribiéndose a UniversalSaleGuiDto del usuario con ID: {}", userId);
        Disposable subscription = LoadingUtil.subscribeWithLoading(
                loadingIndicator,
                universalSaleGuiService.subscribeToChanges(userId),
                this::handleUniversalSaleGuiUpdate,
                this::handleError,
                () -> log.info("Finalizó la suscripción (onComplete).")
        );
        registerSubscription(subscription);

        loadAndPopulateUsersMenu();

        miLogOut.setOnAction(e -> handleLogOut());

        // Inicializar el sistema de bloqueo por inactividad
        initializeInactivityLockSystem();
    }

    /**
     * Actualiza el lblInfo con el nombre del usuario y la primera letra de sus apellidos.
     * Ejemplo: "Juan G."
     */
    private void updateLblInfo() {
        User authUser = securityContext.getAuthenticatedUser();
        if (authUser == null) return;
        String firstLetter = "";
        if (authUser.getApellidos() != null && !authUser.getApellidos().isBlank()) {
            firstLetter = authUser.getApellidos().trim().substring(0, 1);
        }
        lblInfo.setText(authUser.getNombre() + " " + firstLetter + ".");
    }

    /**
     * Solicita la lista de todos los usuarios y llena el menú mAuditar con MenuItems,
     * omitiendo al usuario autenticado. Si se recibe un error con "Access Denied", se oculta mAuditar.
     */
    private void loadAndPopulateUsersMenu() {
        generalService.getAllUsers()
                .collectList()
                .subscribe(
                        users -> runOnUiThread(() -> populateAuditarMenu(users)),
                        error -> {
                            log.error("Error al obtener la lista de usuarios", error);
                            runOnUiThread(() -> {
                                if (error.getMessage() != null && error.getMessage().contains("Access Denied")) {
                                    mAuditar.setVisible(false);
                                } else {
                                    alertUtil.showError("Error al obtener la lista de usuarios: " + error.getMessage());
                                }
                            });
                        }
                );
    }

    /**
     * Construye los MenuItem dentro de mAuditar a partir de la lista de usuarios,
     * excluyendo al usuario autenticado.
     */
    private void populateAuditarMenu(java.util.List<User> users) {
        UUID currentUserId = securityContext.getAuthenticatedUser().getId();
        mAuditar.getItems().clear();
        for (User user : users) {
            if (user.getId().equals(currentUserId)) continue;
            String label = user.getNombre() + " " +
                    (user.getDocumento() != null ? user.getDocumento() : "(doc)");
            MenuItem item = new MenuItem(label);
            item.setOnAction(e -> handleAddAuditingForUser(user));
            mAuditar.getItems().add(item);
        }
    }

    /**
     * Maneja la acción de auditar el MainSaleGui de otro usuario:
     * 1) Obtiene el MainSaleGui del usuario (por su username).
     * 2) Llama a universalSaleGuiService.addAuditedMainSale(...) para agregar su ID a la auditoría.
     */
    private void handleAddAuditingForUser(User otherUser) {
        if (currentUGuiDto == null) {
            alertUtil.showError("No hay UniversalSaleGui activo. Intente nuevamente.");
            return;
        }
        mainSaleGuiService.getMainSaleGuiOfUser(otherUser.getUsername())
                .flatMap(mainSaleGui ->
                        universalSaleGuiService.addAuditedMainSale(currentUGuiDto.getId(), mainSaleGui.getId()))
                .subscribe(
                        updatedDto -> log.info("Se agregó auditoría para el MainSaleGui de '{}'.", otherUser.getUsername()),
                        error -> {
                            log.error("Error agregando auditoría para '{}'.", otherUser.getUsername(), error);
                            runOnUiThread(() ->
                                    alertUtil.showError("No se pudo auditar al usuario " + otherUser.getUsername() +
                                            ": " + error.getMessage()));
                        }
                );
    }

    /**
     * Se invoca cada vez que el servidor emite un nuevo UniversalSaleGuiDto.
     */
    private void handleUniversalSaleGuiUpdate(UniversalSaleGuiDto updated) {
        runOnUiThread(() -> {
            this.currentUGuiDto = updated;
            log.debug("Recibido UniversalSaleGuiDto: {}", updated);
            buildOrUpdatePrincipalTab();
            updateAuditingTabs();
        });
    }

    /**
     * Construye o actualiza el tab principal (MainSaleGui del usuario autenticado),
     * basándose en currentUGuiDto.getMainSaleGuiId().
     */
    private void buildOrUpdatePrincipalTab() {
        UUID mainSaleGuiId = currentUGuiDto.getMainSaleGuiId();
        if (mainSaleGuiId == null) {
            log.warn("El UniversalSaleGuiDto no contiene mainSaleGuiId.");
            return;
        }
        if (tabPrincipal.getContent() == null) {
            Parent mainSaleView = springFXMLLoader.load(FXMLEnum.MAIN_SALE);
            MainSaleGuiController controller = springFXMLLoader.getController(mainSaleView);
            controller.setMainSaleGuiId(mainSaleGuiId);
            tabPrincipal.setContent(mainSaleView);
        } else {
            log.debug("El MainSaleGui ya está asignado en el tab principal. Refrescar si se requiere nueva información.");
        }
    }

    /**
     * Actualiza los tabs de auditoría en función de currentUGuiDto.getAuditingMainSales():
     * - Elimina aquellos que ya no estén.
     * - Agrega nuevos tabs para los IDs que aparezcan.
     */
    private void updateAuditingTabs() {
        Set<UUID> auditingIdsFromServer = currentUGuiDto.getAuditingMainSales();
        if (auditingIdsFromServer == null) {
            auditingIdsFromServer = Collections.emptySet();
        }
        // Remover tabs que ya no existen
        for (Iterator<Map.Entry<UUID, Tab>> it = auditingTabs.entrySet().iterator(); it.hasNext(); ) {
            Map.Entry<UUID, Tab> entry = it.next();
            UUID mainSaleId = entry.getKey();
            if (!auditingIdsFromServer.contains(mainSaleId)) {
                tpUniversal.getTabs().remove(entry.getValue());
                it.remove();
                log.info("Removido tab de auditoría para MainSaleGuiId={}", mainSaleId);
            }
        }
        // Agregar tabs nuevos
        for (UUID mainSaleGuiId : auditingIdsFromServer) {
            if (!auditingTabs.containsKey(mainSaleGuiId)) {
                Tab newAuditingTab = createAuditingTab(mainSaleGuiId);
                auditingTabs.put(mainSaleGuiId, newAuditingTab);
                tpUniversal.getTabs().add(newAuditingTab);
                log.info("Agregado tab de auditoría para MainSaleGuiId={}", mainSaleGuiId);
            }
        }
    }

    /**
     * Crea un tab para auditar el MainSaleGui de otro usuario (por su ID).
     * El título del tab se actualizará con el usernameOwner obtenido del flujo.
     * El tab es cerrable y, al cerrarse, se quita del auditing en el servidor.
     */
    private Tab createAuditingTab(UUID mainSaleGuiId) {
        Tab tab = new Tab();
        tab.setClosable(true);
        Parent mainSaleView = springFXMLLoader.load(FXMLEnum.MAIN_SALE);
        MainSaleGuiController controller = springFXMLLoader.getController(mainSaleView);
        controller.setMainSaleGuiId(mainSaleGuiId);
        tab.setContent(mainSaleView);
        // Suscribirse para actualizar el título con el usernameOwner
        Disposable sub = mainSaleGuiService.subscribeToChanges(mainSaleGuiId)
                .take(1)
                .subscribe(
                        mainSale -> runOnUiThread(() -> {
                            String ownerName = mainSale.getUsernameOwner();
                            tab.setText("Auditar - " + ownerName);
                        }),
                        error -> log.error("No se pudo obtener usernameOwner de MainSaleGuiId={}", mainSaleGuiId, error)
                );
        registerSubscription(sub);
        // Al cerrar el tab, se remueve la auditoría en el servidor
        tab.setOnCloseRequest(event -> {
            if (currentUGuiDto == null) {
                log.warn("No hay currentUGuiDto. No se puede remover auditoría.");
                return;
            }
            UniversalSaleGuiService.RemoveAuditedMainSaleRequest request =
                    new UniversalSaleGuiService.RemoveAuditedMainSaleRequest(currentUGuiDto.getId(), mainSaleGuiId);
            universalSaleGuiService.removeAuditedMainSale(request.universalSaleGuiId(), request.mainSaleGuiId())
                    .subscribe(
                            updatedDto -> log.info("Se removió la auditoría para MainSaleGuiId={}", mainSaleGuiId),
                            error -> {
                                log.error("Error al remover auditoría", error);
                                runOnUiThread(() -> {
                                    alertUtil.showError("No se pudo remover auditoría: " + error.getMessage());
                                    event.consume();
                                });
                            }
                    );
        });
        return tab;
    }

    private void handleError(Throwable error) {
        log.error("Error en la suscripción de UniversalSaleGuiDto: {}", error.getMessage(), error);
        runOnUiThread(() ->
                alertUtil.showError("Error al recibir datos de UniversalSaleGui: " + error.getMessage()));
    }

    /**
     * Lógica para desloguear:
     * 1) Cerrar la vista actual (cancelar suscripciones con onClose()).
     * 2) Llamar a connectionService.logout() para limpiar el contexto.
     * 3) Navegar a la vista LOGIN_RFID usando navigationService.
     */
    private void handleLogOut() {
        log.info("Logout seleccionado. Cerrando la vista y navegando a LoginRFID.");
        onClose();
        connectionService.logout();
        navigationService.navigateTo(FXMLEnum.LOGIN_RFID)
                .subscribe(
                        unused -> log.info("Navegación a LOGIN_RFID exitosa."),
                        error -> runOnUiThread(() ->
                                alertUtil.showError("Error al navegar a pantalla de login: " + error.getMessage()))
                );
    }

    /**
     * Inicializa el sistema de bloqueo por inactividad mejorado.
     *
     * Detecta actividad completa del usuario incluyendo:
     * - Movimientos de mouse (incluyendo rueda del mouse)
     * - Clicks y drags
     * - Eventos de teclado
     * - Interacciones con sliders y controles
     * - Eventos touch y gestos (zoom, rotate, swipe)
     * - Scroll en cualquier dirección
     */
    private void initializeInactivityLockSystem() {
        createScreenLockOverlay();
        setupInactivityTimeline();
        setupActivityEventHandlers();
        startInactivityTimer();

        log.info("Sistema de bloqueo por inactividad inicializado con detección completa de eventos");
    }

    /**
     * Crea el overlay de bloqueo de pantalla con el campo RFID.
     */
    private void createScreenLockOverlay() {
        screenLockOverlay = new VBox();
        screenLockOverlay.setStyle("-fx-background-color: rgba(0, 0, 0, 0.8); -fx-alignment: center; -fx-spacing: 20;");
        screenLockOverlay.setVisible(false);
        screenLockOverlay.setManaged(false);

        rfidLockField = new PasswordField();
        rfidLockField.setPromptText("Ingrese RFID");
        rfidLockField.setStyle("-fx-font-size: 18px; -fx-pref-width: 300px; -fx-alignment: center;");
        rfidLockField.setOnKeyPressed(this::handleRfidFieldKeyPress);

        screenLockOverlay.getChildren().add(rfidLockField);
        spUniversal.getChildren().add(screenLockOverlay);
    }

    /**
     * Configura el Timeline para detectar inactividad.
     */
    private void setupInactivityTimeline() {
        inactivityTimeline = new Timeline(new KeyFrame(
                Duration.seconds(INACTIVITY_TIMEOUT_SECONDS),
                e -> lockScreen()
        ));
        inactivityTimeline.setCycleCount(1);
    }

    /**
     * Configura un handler genérico para detectar CUALQUIER actividad del usuario.
     * Enfoque simplificado que captura todos los eventos de entrada de forma elegante.
     * También configura el handler específico para CTRL+F4 que bloquea inmediatamente.
     */
    private void setupActivityEventHandlers() {
        // Handler específico para CTRL+F4 - debe ir ANTES del handler genérico
        // para interceptar la combinación antes de que se resetee el timer
        keyEventHandler = this::handleKeyPressed;
        spUniversal.addEventFilter(KeyEvent.KEY_PRESSED, keyEventHandler);

        // Handler genérico que resetea el timer para CUALQUIER evento de entrada
        globalActivityHandler = e -> resetInactivityTimer();

        // Usar addEventFilter con InputEvent captura TODOS los eventos de entrada:
        // - Mouse: movimiento, clicks, drag, rueda
        // - Teclado: cualquier tecla
        // - Touch: gestos táctiles
        // - Scroll: en cualquier dirección
        // - Cualquier otro evento de entrada futuro
        spUniversal.addEventFilter(InputEvent.ANY, globalActivityHandler);

        log.debug("Configurado handler genérico para detección de TODA actividad de entrada");
        log.debug("Configurado handler específico para CTRL+F4 para bloqueo inmediato");
    }

    /**
     * Maneja eventos de teclas presionadas para detectar la combinación CTRL+F4.
     * Cuando se detecta CTRL+F4, bloquea la pantalla inmediatamente.
     */
    private void handleKeyPressed(KeyEvent event) {
        // Verificar si se presionó CTRL+F4
        if (event.isControlDown() && event.getCode() == KeyCode.F4) {
            log.info("Detectada combinación CTRL+F4 - Bloqueando pantalla inmediatamente");
            lockScreen();
            event.consume(); // Consumir el evento para evitar que se propague
        }
    }

    /**
     * Inicia el timer de inactividad.
     */
    private void startInactivityTimer() {
        if (!isScreenLocked) {
            inactivityTimeline.playFromStart();
        }
    }

    /**
     * Reinicia el timer de inactividad de forma segura.
     * Solo se ejecuta si la pantalla no está bloqueada y el timeline existe.
     */
    private void resetInactivityTimer() {
        if (!isScreenLocked && inactivityTimeline != null) {
            try {
                inactivityTimeline.stop();
                inactivityTimeline.playFromStart();
                // Log solo ocasionalmente para evitar spam
                if (Math.random() < 0.001) { // ~0.1% de las veces
                    log.trace("Timer de inactividad reiniciado");
                }
            } catch (Exception e) {
                log.warn("Error al reiniciar timer de inactividad: {}", e.getMessage());
            }
        }
    }

    /**
     * Bloquea la pantalla mostrando el overlay.
     */
    private void lockScreen() {
        if (!isScreenLocked) {
            log.info("Bloqueando pantalla por inactividad");
            isScreenLocked = true;
            screenLockOverlay.setVisible(true);
            screenLockOverlay.setManaged(true);
            rfidLockField.clear();
            Platform.runLater(() -> rfidLockField.requestFocus());
        }
    }

    /**
     * Desbloquea la pantalla ocultando el overlay.
     */
    private void unlockScreen() {
        if (isScreenLocked) {
            log.info("Desbloqueando pantalla");
            isScreenLocked = false;
            screenLockOverlay.setVisible(false);
            screenLockOverlay.setManaged(false);
            startInactivityTimer();
        }
    }

    /**
     * Maneja la pulsación de teclas en el campo RFID del bloqueo.
     */
    private void handleRfidFieldKeyPress(KeyEvent event) {
        if (event.getCode() == KeyCode.ENTER) {
            String rfid = rfidLockField.getText();
            if (rfid != null && !rfid.isBlank()) {
                authenticateWithRfid(rfid);
            }
        }
    }

    /**
     * Autentica con el RFID ingresado y maneja el desbloqueo o cambio de usuario.
     */
    private void authenticateWithRfid(String rfid) {
        log.info("Intentando autenticar usuario por RFID para desbloqueo: {}", rfid);

        // Usar BaseController's reactive helper para mejor manejo de errores
        subscribeMonoWithUiUpdate(
                connectionService.findUserByRfid(rfid),
                response -> handleRfidAuthentication(response, rfid),
                this::handleRfidAuthenticationError
        );
    }

    /**
     * Maneja la autenticación con RFID - unificado y optimizado.
     */
    private void handleRfidAuthentication(LoginResponse response, String rfid) {
        User currentUser = securityContext.getAuthenticatedUser();
        User rfidUser = response.getUser();

        if (currentUser != null && currentUser.getId().equals(rfidUser.getId())) {
            // Mismo usuario - solo desbloquear
            log.info("Mismo usuario autenticado ({}). Desbloqueando pantalla.", currentUser.getUsername());
            unlockScreen();
        } else {
            // Usuario diferente - cambio optimizado sin navegación
            log.info("Usuario diferente detectado. Cambiando de {} a {}",
                    currentUser != null ? currentUser.getUsername() : "null",
                    rfidUser.getUsername());
            performOptimizedUserSwitch(rfid);
        }
    }

    /**
     * Maneja errores en la autenticación con RFID.
     */
    private void handleRfidAuthenticationError(Throwable error) {
        log.error("Error en autenticación RFID para desbloqueo: {}", error.getMessage());

        // Limpiar campo y mostrar error visual
        rfidLockField.clear();
        rfidLockField.setStyle("-fx-border-color: red; -fx-font-size: 18px; -fx-pref-width: 300px; -fx-alignment: center;");

        // Mostrar mensaje de error apropiado
        String errorMessage = "RFID inválido o usuario no encontrado";
        if (error.getMessage() != null && error.getMessage().contains("network")) {
            errorMessage = "Error de conexión. Verifique la red.";
        }
        alertUtil.showError(errorMessage);

        // Restaurar estilo normal y focus después de 2 segundos
        Timeline resetStyle = new Timeline(new KeyFrame(Duration.seconds(2), e -> {
            rfidLockField.setStyle("-fx-font-size: 18px; -fx-pref-width: 300px; -fx-alignment: center;");
            rfidLockField.requestFocus();
        }));
        resetStyle.play();

        // Asegurar focus inmediatamente después del error
        rfidLockField.requestFocus();
    }

    /**
     * Realiza el cambio optimizado de usuario sin navegación completa.
     * Actualiza el contexto de seguridad y recarga los datos in-situ.
     */
    private void performOptimizedUserSwitch(String rfid) {
        log.info("Iniciando cambio optimizado de usuario para RFID: {}", rfid);

        // 1. Limpiar suscripciones actuales pero mantener la vista
        clearCurrentSubscriptions();

        // 2. Hacer loginByRfid para actualizar el contexto de seguridad
        subscribeMonoWithUiUpdate(
                connectionService.loginByRfid(rfid),
                response -> {
                    log.info("Login exitoso para nuevo usuario: {}", response.getUser().getUsername());
                    // 3. Recargar datos para el nuevo usuario sin navegación
                    reloadDataForNewUser();
                },
                error -> {
                    log.error("Error en cambio de usuario con RFID: {}", error.getMessage());
                    handleRfidAuthenticationError(error);
                }
        );
    }

    /**
     * Limpia las suscripciones actuales sin cerrar la vista.
     */
    private void clearCurrentSubscriptions() {
        log.debug("Limpiando suscripciones actuales para cambio de usuario");
        // Cancelar suscripciones pero no llamar onClose() completo
        // ya que queremos mantener la vista activa
        clearSubscriptions();
    }

    /**
     * Recarga los datos para el nuevo usuario autenticado de forma optimizada.
     */
    private void reloadDataForNewUser() {
        log.info("Recargando datos para el nuevo usuario autenticado");

        // Verificar que hay usuario autenticado
        if (!securityContext.isAuthenticated() || securityContext.getAuthenticatedUser() == null) {
            log.error("No hay usuario autenticado después del login. Abortando recarga.");
            handleRfidAuthenticationError(new IllegalStateException("No hay usuario autenticado"));
            return;
        }

        User newUser = securityContext.getAuthenticatedUser();
        log.info("Recargando datos para usuario: {}", newUser.getUsername());

        // 1. Actualizar información del usuario en la UI
        updateLblInfo();

        // 2. Limpiar tabs de auditoría existentes
        clearAuditingTabs();

        // 3. Reinicializar suscripciones para el nuevo usuario
        UUID userId = newUser.getId();
        log.info("Reinicializando suscripciones para usuario con ID: {}", userId);
        Disposable subscription = LoadingUtil.subscribeWithLoading(
                loadingIndicator,
                universalSaleGuiService.subscribeToChanges(userId),
                this::handleUniversalSaleGuiUpdate,
                this::handleError,
                () -> log.info("Finalizó la suscripción (onComplete).")
        );
        registerSubscription(subscription);

        // 4. Recargar menú de usuarios
        loadAndPopulateUsersMenu();

        // 5. Desbloquear pantalla
        unlockScreen();

        log.info("Datos recargados exitosamente para usuario: {}", newUser.getUsername());
    }

    /**
     * Limpia todos los tabs de auditoría existentes.
     */
    private void clearAuditingTabs() {
        log.debug("Limpiando tabs de auditoría existentes");

        // Remover todos los tabs excepto el principal
        tpUniversal.getTabs().removeIf(tab -> tab != tabPrincipal);

        // Limpiar el mapa de tabs de auditoría
        auditingTabs.clear();

        // Limpiar el contenido del tab principal para que se recargue
        tabPrincipal.setContent(null);

        log.debug("Tabs de auditoría limpiados exitosamente");
    }



    /**
     * Limpia los event handlers de actividad para evitar memory leaks.
     */
    private void cleanupActivityHandlers() {
        if (spUniversal != null) {
            // Limpiar handler específico de teclado (CTRL+F4)
            if (keyEventHandler != null) {
                log.debug("Limpiando handler específico de teclado (CTRL+F4)");
                spUniversal.removeEventFilter(KeyEvent.KEY_PRESSED, keyEventHandler);
                keyEventHandler = null;
            }
            
            // Limpiar handler genérico de actividad
            if (globalActivityHandler != null) {
                log.debug("Limpiando handler genérico de actividad");
                spUniversal.removeEventFilter(InputEvent.ANY, globalActivityHandler);
                globalActivityHandler = null;
            }
        }
    }

    @Override
    public void onClose() {
        // Detener el timer de inactividad
        if (inactivityTimeline != null) {
            inactivityTimeline.stop();
        }

        // Limpiar event handlers
        cleanupActivityHandlers();

        super.onClose();
        log.info("UnisersalSaleGuiController cerrado.");
    }
}
