package corp.jamaro.jamaroescritoriofx.appfx.ventas.controller;

import corp.jamaro.jamaroescritoriofx.appfx.controller.BaseController;
import corp.jamaro.jamaroescritoriofx.appfx.cliente.util.ClienteCreationHelper;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Item;
import corp.jamaro.jamaroescritoriofx.appfx.producto.model.Producto;
import corp.jamaro.jamaroescritoriofx.appfx.producto.service.ItemMantenimientoService;
import corp.jamaro.jamaroescritoriofx.appfx.service.ClienteService;
import corp.jamaro.jamaroescritoriofx.appfx.service.SpringFXMLLoader;
import corp.jamaro.jamaroescritoriofx.appfx.util.AlertUtil;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.util.SaleDialogUtil;
import corp.jamaro.jamaroescritoriofx.appfx.util.LoadingUtil;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.BienServicioCargado;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.BienServicioDevuelto;
import corp.jamaro.jamaroescritoriofx.appfx.model.Cliente;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.model.Sale;
import corp.jamaro.jamaroescritoriofx.appfx.ventas.service.SaleService;
import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.scene.control.ButtonBar;
import javafx.scene.Parent;
import javafx.scene.control.*;
import javafx.scene.layout.AnchorPane;
import javafx.scene.layout.StackPane;
import javafx.scene.layout.VBox;
import javafx.util.Callback;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.controlsfx.control.textfield.AutoCompletionBinding;
import org.controlsfx.control.textfield.CustomTextField;
import org.controlsfx.control.textfield.TextFields;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import reactor.core.Disposable;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.net.URL;
import java.text.NumberFormat;
import java.time.Duration;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.UUID;

@Component
@Scope("prototype")
@RequiredArgsConstructor
@Slf4j
public class SaleController extends BaseController {

    // Getters para los ListViews (necesarios para que SaleGuiController pueda acceder a ellos)
    public ListView<BienServicioCargado> getBienServicioCargados() {
        return bienServicioCargados;
    }

    public ListView<BienServicioDevuelto> getBienServicioDevueltos() {
        // Returning null since bienServicioDevueltos has been removed
        return null;
    }

    // Getters para los campos de texto (necesarios para que SaleGuiController pueda acceder a ellos)
    public TextField getTxtCodigo() {
        return txtCodigo;
    }

    public CustomTextField getTxtDocumentoNombreRazon() {
        return txtDocumentoNombreRazon;
    }

    private final SaleService saleService;
    private final ClienteService clienteService;
    private final AlertUtil alertUtil;
    private final SpringFXMLLoader springFXMLLoader;
    private final SaleDialogUtil saleDialogUtil;
    private final ClienteCreationHelper clienteCreationHelper;
    private final ItemMantenimientoService itemMantenimientoService;

    private UUID saleId;
    private Sale currentSale;
    private Disposable saleSubscription;
    private NumberFormat currencyFormat = NumberFormat.getCurrencyInstance();

    // Autocompletado para clientes
    private AutoCompletionBinding<String> autoCompletionBinding;

    // Debounce para el botón limpiar (en milisegundos)
    private static final long LIMPIAR_DEBOUNCE_DELAY = 1000;
    private long lastLimpiarClickTime = 0;

    // Callback para notificar al controlador padre cuando se debe cerrar el SaleGui
    private Runnable onSaleGuiCloseRequested;


    @FXML
    private AnchorPane anchorBotones;

    @FXML
    private AnchorPane anchorSale;

    @FXML
    private ListView<BienServicioCargado> bienServicioCargados;

    // bienServicioDevueltos has been removed from the FXML



    @FXML
    private Button btnImprimir;

    @FXML
    private Button btnLimpiar;

    @FXML
    private Button btnVender;

    @FXML
    private Label lblDescuento;

    @FXML
    private Label lblTotalMontoAcordado;

    @FXML
    private Label lblTotalMontoInicial;

    @FXML
    private ProgressIndicator loadingIndicator;

    // splitPaneBienesServicios has been removed from the FXML

    @FXML
    private StackPane stackPaneSale;

    @FXML
    private TextField txtCodigo;

    @FXML
    private CustomTextField txtDocumentoNombreRazon;

    @FXML
    private VBox vbTotalVenta;

    /**
     * Establece el ID de la venta y se suscribe a sus actualizaciones.
     * @param saleId ID de la venta
     */
    public void setSaleId(UUID saleId) {
        this.saleId = saleId;
        log.debug("SaleController: Configurando saleId={}", saleId);

        // Inicializar la UI
        initializeUI();

        // Suscribirse a las actualizaciones de la venta
        subscribeToSaleUpdates();
    }

    /**
     * Inicializa la interfaz de usuario.
     */
    private void initializeUI() {
        // Configurar el cell factory para el ListView de BienServicioCargado
        // Usar un enfoque con cache para evitar recargar el FXML constantemente
        bienServicioCargados.setCellFactory(new Callback<>() {
            @Override
            public ListCell<BienServicioCargado> call(ListView<BienServicioCargado> param) {
                return new ListCell<>() {
                    // Cache para las celdas, usando el ID de BienServicioCargado como clave
                    private final Map<String, Parent> cellCache = new HashMap<>();
                    private final Map<String, BienServicioCargadoController> controllerCache = new HashMap<>();

                    @Override
                    protected void updateItem(BienServicioCargado item, boolean empty) {
                        super.updateItem(item, empty);

                        if (empty || item == null) {
                            setText(null);
                            setGraphic(null);
                            // Deshabilitar eventos de mouse en celdas vacías
                            setDisable(true);
                            setMouseTransparent(true);
                        } else {
                            // Habilitar eventos de mouse en celdas con contenido
                            setDisable(false);
                            setMouseTransparent(false);

                            // Usar el ID del BienServicioCargado como clave para el cache
                            String bienServicioId = item.getId().toString();

                            try {
                                Parent cellContent;
                                BienServicioCargadoController controller;

                                // Verificar si ya existe en el cache
                                if (cellCache.containsKey(bienServicioId)) {
                                    cellContent = cellCache.get(bienServicioId);
                                    controller = controllerCache.get(bienServicioId);
                                } else {
                                    // Crear una nueva instancia y guardarla en el cache
                                    cellContent = springFXMLLoader.load("fxml/sale/bienServicioCargado.fxml");
                                    controller = springFXMLLoader.getController(cellContent);

                                    // Guardar en el cache
                                    cellCache.put(bienServicioId, cellContent);
                                    controllerCache.put(bienServicioId, controller);
                                }

                                // Configurar el controlador con el item actual
                                controller.setBienServicioCargado(item, saleId);
                                setGraphic(cellContent);
                            } catch (Exception e) {
                                log.error("Error al cargar bienServicioCargado.fxml: {}", e.getMessage(), e);
                                setText("Error al cargar la vista");
                            }
                        }
                    }
                };
            }
        });

        // Configurar el ListView para que se ajuste correctamente
        // Usar altura fija para mejor rendimiento y consistencia visual
        bienServicioCargados.setFixedCellSize(65.0); // Altura fija de 65px como está definida en el FXML

        // Configurar el ancho para que use el espacio disponible
        bienServicioCargados.setPrefWidth(javafx.scene.layout.Region.USE_COMPUTED_SIZE);

        // Agregar detector de clic para evitar clics en celdas vacías
        bienServicioCargados.setOnMouseClicked(event -> {
            // Verificar si el clic ocurrió en una celda vacía
            int clickedIndex = bienServicioCargados.getSelectionModel().getSelectedIndex();
            if (clickedIndex >= bienServicioCargados.getItems().size() || clickedIndex < 0) {
                // Clic en celda vacía, ignorar
                log.debug("Clic detectado en celda vacía del ListView de BienServicioCargado, ignorando");
                event.consume();
            }
        });

        // Configuración simplificada sin listeners complejos
        // JavaFX manejará automáticamente el redimensionamiento

        // BienServicioDevuelto ListView configuration has been removed

        // Configurar autocompletado para clientes
        configureClienteAutoComplete();
    }

    /**
     * Se suscribe a las actualizaciones de la venta desde el servidor.
     */
    private void subscribeToSaleUpdates() {
        if (saleId == null) {
            log.warn("No se puede suscribir a actualizaciones: saleId es null");
            return;
        }

        // Cancelar suscripción anterior si existe
        if (saleSubscription != null && !saleSubscription.isDisposed()) {
            saleSubscription.dispose();
        }

        // Suscribirse a las actualizaciones de la venta
        saleSubscription = LoadingUtil.subscribeWithLoading(
                loadingIndicator,
                saleService.subscribeToSaleUpdates(saleId)
                        .retryWhen(Retry.backoff(3, Duration.ofMillis(200))
                                .filter(err -> err.getMessage() != null
                                        && err.getMessage().toLowerCase().contains("deadlock"))
                                .doBeforeRetry(signal ->
                                        log.warn("Reintentando suscripción a Sale (intento #{}) por: {}",
                                                signal.totalRetries() + 1, signal.failure().getMessage()))
                        ),
                this::onSaleUpdate,
                this::handleSubscriptionError,
                () -> log.debug("Suscripción a Sale completada.")
        );
        registerSubscription(saleSubscription);
    }

    /**
     * Maneja las actualizaciones recibidas de la venta.
     * @param sale La venta actualizada
     */
    private void onSaleUpdate(Sale sale) {
        runOnUiThread(() -> {
            log.debug("Recibida actualización de Sale: {}", sale.getId());
            this.currentSale = sale;

            // Actualizar la UI con los datos de la venta
            updateUI(sale);
        });
    }

    /**
     * Actualiza la interfaz de usuario con los datos de la venta.
     * @param sale La venta con los datos actualizados
     */
    private void updateUI(Sale sale) {
        // Actualizar información del cliente
        if (sale.getCliente() != null) {
            String clienteInfo = "";

            // Obtener el documento principal (DNI, RUC u otro)
            if (sale.getCliente().getDni() != null && !sale.getCliente().getDni().isEmpty()) {
                clienteInfo = sale.getCliente().getDni();
            } else if (sale.getCliente().getRuc() != null && !sale.getCliente().getRuc().isEmpty()) {
                clienteInfo = sale.getCliente().getRuc();
            } else if (sale.getCliente().getOtroDocumento() != null && !sale.getCliente().getOtroDocumento().isEmpty()) {
                clienteInfo = sale.getCliente().getOtroDocumento();
            }

            // Agregar nombre o razón social
            String nombreCompleto = "";
            if (sale.getCliente().getRazonSocial() != null && !sale.getCliente().getRazonSocial().isEmpty()) {
                nombreCompleto = sale.getCliente().getRazonSocial();
            } else {
                // Construir nombre completo a partir de nombre y apellido
                if (sale.getCliente().getNombre() != null && !sale.getCliente().getNombre().isEmpty()) {
                    nombreCompleto = sale.getCliente().getNombre();
                    if (sale.getCliente().getApellido() != null && !sale.getCliente().getApellido().isEmpty()) {
                        nombreCompleto += " " + sale.getCliente().getApellido();
                    }
                }
            }

            // Combinar documento y nombre
            if (!clienteInfo.isEmpty() && !nombreCompleto.isEmpty()) {
                clienteInfo += " - " + nombreCompleto;
            } else if (!nombreCompleto.isEmpty()) {
                clienteInfo = nombreCompleto;
            }

            txtDocumentoNombreRazon.setText(clienteInfo);
        } else {
            txtDocumentoNombreRazon.setText("");
        }

        // Actualizar totales
        double totalInicial = 0.0;
        double totalAcordado = 0.0;

        if (sale.getBienServicioCargados() != null) {
            for (BienServicioCargado bsc : sale.getBienServicioCargados()) {
                if (bsc.getPrecioInicial() != null && bsc.getCantidad() != null) {
                    totalInicial += bsc.getPrecioInicial() * bsc.getCantidad();
                }

                // Usar montoAcordado si está disponible, sino calcular con precioAcordado o precioInicial
                if (bsc.getMontoAcordado() != null) {
                    totalAcordado += bsc.getMontoAcordado();
                } else if (bsc.getPrecioAcordado() != null && bsc.getCantidad() != null) {
                    totalAcordado += bsc.getPrecioAcordado() * bsc.getCantidad();
                } else if (bsc.getPrecioInicial() != null && bsc.getCantidad() != null) {
                    totalAcordado += bsc.getPrecioInicial() * bsc.getCantidad();
                }
            }
        }

        // Si hay totales en el objeto Sale, usarlos en lugar de los calculados
        if (sale.getTotalMontoInicial() != null) {
            totalInicial = sale.getTotalMontoInicial();
        }
        if (sale.getTotalMontoAcordado() != null) {
            totalAcordado = sale.getTotalMontoAcordado();
        }

        // Calcular descuento como la diferencia entre total inicial y total acordado
        double descuento = totalInicial - totalAcordado;

        lblTotalMontoInicial.setText(currencyFormat.format(totalInicial));
        lblDescuento.setText(currencyFormat.format(descuento));
        lblTotalMontoAcordado.setText(currencyFormat.format(totalAcordado));

        // Actualizar listas de items
        bienServicioCargados.getItems().clear();
        if (sale.getBienServicioCargados() != null) {
            // Ordenar los BienServicioCargado por createdAt de más antiguo a más reciente
            List<BienServicioCargado> sortedItems = sale.getBienServicioCargados().stream()
                    .sorted(Comparator.comparing(BienServicioCargado::getCreatedAt,
                            Comparator.nullsLast(Comparator.naturalOrder())))
                    .toList();

            log.debug("Ordenando {} BienServicioCargado por createdAt (más antiguo a más reciente)", sortedItems.size());
            bienServicioCargados.getItems().addAll(sortedItems);
        }

        // BienServicioDevueltos handling has been removed
    }

    /**
     * Maneja errores en la suscripción a la venta.
     * @param error El error ocurrido
     */
    private void handleSubscriptionError(Throwable error) {
        log.error("Error en la suscripción de Sale: {}", error.getMessage(), error);
        runOnUiThread(() ->
                alertUtil.showError("Error al recibir datos de la venta: " + error.getMessage())
        );
    }

    /**
     * Método agnostico para agregar un Item a la venta cuando se hace doble clic en él.
     * Este método será llamado desde SearchProductGuiController.
     *
     * @param item El Item a agregar
     * @param producto El Producto padre del Item
     */
    public void addItemToSale(Item item, Producto producto) {
        if (saleId == null) {
            log.warn("No se puede agregar item: saleId es null");
            alertUtil.showError("No hay una venta activa para agregar el item.");
            return;
        }

        if (item == null || item.getCodCompuesto() == null) {
            log.warn("No se puede agregar item: item o codCompuesto es null");
            alertUtil.showError("El item seleccionado no es válido.");
            return;
        }

        // Usar SaleDialogUtil para mostrar el diálogo de cantidad con funcionalidad mejorada
        // El diálogo ahora soporta formato mejorado: "cantidad [espacios] precio_inicial"
        Optional<String> quantityResult = saleDialogUtil.showCantidadDialog(
                item.getCodCompuesto(),
                item.getDescripcion()
        );
        if (quantityResult.isPresent()) {
            try {
                // FUNCIONALIDAD MEJORADA: Parsing avanzado que soporta cantidad y precio inicial
                // Ejemplos de input válido:
                // - Solo cantidad: "12", "50.5", "40"
                // - Cantidad + precio: "12 EU.L", "50.5   305.5", "40  co"
                // - El precio puede ser numérico o encriptado usando EncryptionUtil
                SaleDialogUtil.CantidadPrecioResult parseResult = saleDialogUtil.parseEnhancedInput(quantityResult.get());
                
                double cantidad = parseResult.cantidad();
                Double precioInicial = parseResult.precioInicial();

                // Verificar que la cantidad sea positiva
                if (cantidad <= 0) {
                    alertUtil.showError("La cantidad debe ser mayor que cero.");
                    return;
                }

                // Si no se especificó precio inicial en el input, usar el precio de venta público como por defecto
                if (precioInicial == null && item.getPrecioVentaPublico() != null) {
                    precioInicial = item.getPrecioVentaPublico();
                    log.debug("Usando precio de venta público como precio inicial por defecto: {}", precioInicial);
                } else if (precioInicial != null) {
                    log.info("Usando precio inicial especificado por el usuario: {}", precioInicial);
                }

                // Enviar la solicitud al servidor
                runOnUiThread(() -> loadingIndicator.setVisible(true));
                subscribeMonoWithUiUpdate(
                        saleService.addItemToSale(saleId, item.getCodCompuesto(), precioInicial, cantidad),
                        response -> {
                            loadingIndicator.setVisible(false);
                            if (response.success()) {
                                log.info("Item agregado exitosamente a la venta: {}", item.getCodCompuesto());
                                // No necesitamos actualizar la UI aquí, ya que recibiremos una actualización a través de la suscripción
                            } else {
                                log.warn("Error al agregar item a la venta: {}", response.message());
                                alertUtil.showError("Error al agregar item: " + response.message());
                            }
                        },
                        error -> {
                            loadingIndicator.setVisible(false);
                            log.error("Error al agregar item a la venta: {}", error.getMessage(), error);
                            alertUtil.showError("Error al agregar item: " + error.getMessage());
                        }
                );

            } catch (NumberFormatException e) {
                // Proporcionar mensaje de error más específico para el formato mejorado
                String errorMessage = "Error en el formato de entrada: " + e.getMessage() + 
                    "\n\nFormatos válidos:" +
                    "\n• Solo cantidad: 12, 50.5, 40" +
                    "\n• Cantidad + precio: 12 EU.L, 50.5   305.5, 40  co" +
                    "\n\nEl precio puede ser numérico o encriptado.";
                
                log.warn("Error al parsear input del usuario: {}", e.getMessage());
                alertUtil.showError(errorMessage);
            }
        }
    }

    /**
     * Maneja el evento del botón Limpiar para eliminar todos los BienServicioCargado.
     */
    @FXML
    public void handleBtnLimpiar() {
        debouncedLimpiar();
    }

    /**
     * Maneja el evento del botón Vender según el tipo de venta actual.
     */
    @FXML
    public void handleBtnVender() {
        if (saleId == null || currentSale == null) {
            log.warn("No se puede procesar venta: saleId o currentSale es null");
            alertUtil.showError("No hay una venta activa para procesar.");
            return;
        }

        if (currentSale.getBienServicioCargados() == null || currentSale.getBienServicioCargados().isEmpty()) {
            log.warn("No se puede procesar venta: no hay items cargados");
            alertUtil.showError("No hay items en la venta para procesar.");
            return;
        }

        // Mostrar diálogo para seleccionar tipo de venta
        saleDialogUtil.showTipoVentaSelectionDialogForSale().ifPresent(selectedTipoVenta -> {
            log.debug("Tipo de venta seleccionado: {}", selectedTipoVenta);
            loadingIndicator.setVisible(true);

            Mono<SaleService.OperationResponse> operationMono;

            switch (selectedTipoVenta) {
                case PROFORMA -> {
                    log.debug("Iniciando venta de contado desde PROFORMA");
                    operationMono = saleService.iniciarVentaContado(saleId);
                }
                case CREDITO -> {
                    log.debug("Iniciando venta de crédito");
                    operationMono = saleService.iniciarVentaCredito(saleId);
                }
                case PEDIDO -> {
                    log.debug("Iniciando venta de pedido");
                    operationMono = saleService.iniciarVentaPedido(saleId);
                }
                default -> {
                    loadingIndicator.setVisible(false);
                    log.warn("Tipo de venta no soportado para iniciar venta: {}", selectedTipoVenta);
                    alertUtil.showError("Tipo de venta no soportado: " + selectedTipoVenta);
                    return;
                }
            }

            subscribeOnBoundedElastic(
                    operationMono,
                    response -> {
                        loadingIndicator.setVisible(false);
                        if (response.success()) {
                            log.info("Venta iniciada exitosamente con tipo: {}", selectedTipoVenta);
                            runOnUiThread(() -> {
                                saleDialogUtil.showVentaProcesamientoExitoso(selectedTipoVenta, currentSale.getTotalMontoAcordado());
                                // Cerrar el SaleGui después de una venta exitosa
                                closeSaleGui();
                            });
                        } else {
                            log.warn("Error al iniciar venta: {}", response.message());
                            runOnUiThread(() -> {
                                String errorMessage = response.message();

                                // Proporcionar contexto adicional para errores de venta a crédito
                                if (selectedTipoVenta == Sale.TipoVenta.CREDITO) {
                                    if (currentSale != null && currentSale.getCliente() == null) {
                                        errorMessage = "El cliente no puede ser nulo para una venta a crédito. " +
                                                      "Por favor, seleccione un cliente antes de procesar la venta a crédito.";
                                    } else if (errorMessage != null && errorMessage.toLowerCase().contains("cliente")) {
                                        // Si el mensaje del servidor menciona cliente, mantenerlo pero agregar contexto
                                        errorMessage = errorMessage + "\n\nPor favor, verifique que haya seleccionado un cliente válido para la venta a crédito.";
                                    }
                                }

                                alertUtil.showError("Error al procesar venta: " + errorMessage);
                            });
                        }
                    },
                    error -> {
                        loadingIndicator.setVisible(false);
                        log.error("Error al iniciar venta: {}", error.getMessage(), error);
                        runOnUiThread(() -> alertUtil.showError("Error al procesar venta: " + error.getMessage()));
                    }
            );
        });
    }

    /**
     * Maneja el evento del botón Imprimir.
     */
    @FXML
    public void handleBtnImprimir() {
        if (saleId == null || currentSale == null) {
            log.warn("No se puede imprimir: saleId o currentSale es null");
            alertUtil.showError("No hay una venta activa para imprimir.");
            return;
        }

        if (currentSale.getBienServicioCargados() == null || currentSale.getBienServicioCargados().isEmpty()) {
            log.warn("No se puede imprimir: no hay items cargados");
            alertUtil.showError("No hay items en la venta para imprimir.");
            return;
        }

        log.info("Imprimiendo venta con saleId: {}", saleId);

        // Mostrar indicador de carga
        loadingIndicator.setVisible(true);

        // Aquí puedes implementar la lógica de impresión
        // Por ahora, solo mostramos un mensaje informativo
        Platform.runLater(() -> {
            loadingIndicator.setVisible(false);
            alertUtil.showInfo("Función de impresión",
                "La función de impresión será implementada próximamente.\n" +
                "Venta ID: " + saleId + "\n" +
                "Items: " + currentSale.getBienServicioCargados().size() + "\n" +
                "Total: " + currencyFormat.format(currentSale.getTotalMontoAcordado()));
        });
    }

    /**
     * Maneja el evento del campo txtCodigo cuando se presiona Enter.
     * Busca un Item por su código compuesto y lo agrega a la venta si se encuentra.
     */
    private void handleTxtCodigoAction() {
        String codigo = txtCodigo.getText();
        if (codigo == null || codigo.trim().isEmpty()) {
            log.debug("Campo txtCodigo está vacío, ignorando acción");
            return;
        }

        final String codigoFinal = codigo.trim();
        log.debug("Buscando item por código compuesto: {}", codigoFinal);

        // Mostrar indicador de carga en el hilo de UI
        runOnUiThread(() -> loadingIndicator.setVisible(true));

        // Buscar el item por código compuesto usando el patrón correcto
        subscribeMonoWithUiUpdate(
                itemMantenimientoService.buscarItemPorCodCompuesto(codigoFinal)
                        .switchIfEmpty(Mono.fromCallable(() -> {
                            // Este bloque se ejecuta cuando el Mono está vacío (no se encontró item)
                            log.warn("No se encontró item con código compuesto: {}", codigoFinal);
                            return null; // Retornamos null para indicar que no se encontró
                        }))
                        .onErrorResume(error -> {
                            log.error("Error en búsqueda de item: {}", error.getMessage(), error);
                            return Mono.just((Item) null); // Retornar null en caso de error
                        }),
                item -> {
                    // Ocultar indicador de carga
                    loadingIndicator.setVisible(false);

                    if (item != null) {
                        // Item encontrado exitosamente
                        log.info("Item encontrado: {} - {}", item.getCodCompuesto(),
                                item.getMarca() != null ? item.getMarca().getNombre() : "Sin marca");

                        // Limpiar el campo para una nueva búsqueda
                        txtCodigo.clear();

                        // Agregar el item a la venta (reutilizando la misma lógica del doble clic)
                        addItemToSale(item, null);

                        // Después de agregar el item, regresar el focus al campo para continuar
                        Platform.runLater(() -> txtCodigo.requestFocus());
                    } else {
                        // Item no encontrado
                        alertUtil.showError("No se encontró ningún item con el código: " + codigoFinal);

                        // Limpiar el campo y regresar el focus
                        txtCodigo.clear();
                        txtCodigo.requestFocus();
                    }
                },
                error -> {
                    // Error inesperado en la suscripción
                    log.error("Error inesperado al buscar item por código compuesto '{}': {}", codigoFinal, error.getMessage(), error);

                    // Ocultar indicador de carga
                    loadingIndicator.setVisible(false);

                    // Mostrar mensaje de error
                    alertUtil.showError("Error al buscar el item: " + error.getMessage());

                    // Limpiar el campo y regresar el focus
                    txtCodigo.clear();
                    txtCodigo.requestFocus();
                }
        );
    }

    /**
     * Implementa debounce para el botón limpiar.
     */
    private void debouncedLimpiar() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastLimpiarClickTime < LIMPIAR_DEBOUNCE_DELAY) {
            log.debug("Limpiar ignorado por debounce");
            return;
        }
        lastLimpiarClickTime = currentTime;

        performLimpiar();
    }

    /**
     * Ejecuta la limpieza de todos los BienServicioCargado.
     */
    private void performLimpiar() {
        if (saleId == null) {
            log.warn("No se puede limpiar: saleId es null");
            alertUtil.showError("No hay una venta activa para limpiar.");
            return;
        }

        // Confirmar la acción con el usuario
        var result = alertUtil.showConfirmation(
                "Confirmar Limpieza",
                "¿Está seguro de que desea eliminar todos los items de la venta?",
                "Esta acción no se puede deshacer.",
                "Sí, eliminar",
                "Cancelar"
        );

        if (result.isEmpty() || result.get().getButtonData() != ButtonBar.ButtonData.OK_DONE) {
            return;
        }

        // Mostrar indicador de carga
        loadingIndicator.setVisible(true);

        // Llamar al servicio para eliminar todos los BienServicioCargado
        subscribeOnBoundedElastic(
                saleService.deleteAllBienServicioCargado(saleId),
                response -> {
                    loadingIndicator.setVisible(false);
                    if (response.success()) {
                        log.info("Todos los BienServicioCargado eliminados exitosamente de la venta: {}", saleId);
                        // No necesitamos actualizar la UI aquí, ya que recibiremos una actualización a través de la suscripción
                    } else {
                        log.warn("Error al eliminar todos los BienServicioCargado: {}", response.message());
                        runOnUiThread(() -> alertUtil.showError("Error al limpiar la venta: " + response.message()));
                    }
                },
                error -> {
                    loadingIndicator.setVisible(false);
                    log.error("Error al eliminar todos los BienServicioCargado: {}", error.getMessage(), error);
                    runOnUiThread(() -> alertUtil.showError("Error al limpiar la venta: " + error.getMessage()));
                }
        );
    }

    // SplitPane divider visibility management has been removed

    @Override
    public void initialize(URL url, ResourceBundle resourceBundle) {
        // Inicializar componentes básicos
        // La inicialización completa se hace en setSaleId() cuando se recibe el ID de la venta

        // Los atajos de teclado ahora están centralizados en SaleGuiController
        // para que funcionen globalmente independientemente del foco

        // Configurar handler para el botón imprimir (no está configurado en FXML)
        btnImprimir.setOnAction(e -> handleBtnImprimir());

        // Configurar handler para txtCodigo (buscar item por código compuesto)
        txtCodigo.setOnAction(e -> handleTxtCodigoAction());

        log.debug("Inicializando SaleController");

        // Asegurar que el indicador de carga esté oculto inicialmente
        ensureLoadingIndicatorHidden();

        // SplitPane and bienServicioDevueltos initialization has been removed
    }

    @Override
    public void onClose() {
        super.onClose();

        // Cancelar suscripción si existe
        if (saleSubscription != null && !saleSubscription.isDisposed()) {
            saleSubscription.dispose();
        }

        log.info("SaleController cerrado.");
    }

    /**
     * Configura el autocompletado para el campo de cliente.
     */
    private void configureClienteAutoComplete() {
        // Habilitar el campo para edición
        txtDocumentoNombreRazon.setDisable(false);
        txtDocumentoNombreRazon.setEditable(true);

        // Mapa para mantener la relación entre string mostrado y cliente
        java.util.Map<String, Cliente> clienteMap = new java.util.concurrent.ConcurrentHashMap<>();

        // Configurar autocompletado con búsqueda dinámica usando ControlsFX
        autoCompletionBinding = TextFields.bindAutoCompletion(
                txtDocumentoNombreRazon,
                request -> {
                    String searchTerm = request.getUserText();
                    if (searchTerm == null || searchTerm.trim().length() < 2) {
                        return java.util.Collections.emptyList();
                    }

                    try {
                        java.util.List<Cliente> clientes;

                        // Buscar por documento primero (búsqueda exacta)
                        if (searchTerm.matches("\\d+")) {
                            // Si es solo números, buscar por documento
                            clientes = clienteService.searchClientesByDocument(searchTerm)
                                    .take(10) // Limitar a 10 resultados
                                    .collectList()
                                    .block(java.time.Duration.ofSeconds(2));
                        } else {
                            // Si contiene letras, buscar por nombre
                            clientes = clienteService.searchClientesByName(searchTerm)
                                    .take(10) // Limitar a 10 resultados
                                    .collectList()
                                    .block(java.time.Duration.ofSeconds(2));
                        }

                        if (clientes == null) {
                            return java.util.Collections.emptyList();
                        }

                        // Convertir clientes a strings y mantener el mapeo
                        java.util.List<String> suggestions = new java.util.ArrayList<>();
                        clienteMap.clear();

                        for (Cliente cliente : clientes) {
                            String display = formatClienteDisplay(cliente);
                            suggestions.add(display);
                            clienteMap.put(display, cliente);
                        }

                        return suggestions;

                    } catch (Exception e) {
                        log.warn("Error en búsqueda de clientes: {}", e.getMessage());
                        return java.util.Collections.emptyList();
                    }
                }
        );

        // Manejar la selección de un cliente
        autoCompletionBinding.setOnAutoCompleted(event -> {
            String selectedText = event.getCompletion();
            Cliente selectedCliente = clienteMap.get(selectedText);

            if (selectedCliente != null && saleId != null) {
                log.debug("Cliente seleccionado: {}", selectedCliente.getId());

                // Actualizar el cliente en la venta
                loadingIndicator.setVisible(true);
                subscribeOnBoundedElastic(
                        saleService.updateCliente(saleId, selectedCliente.getId()),
                        response -> {
                            loadingIndicator.setVisible(false);
                            if (response.success()) {
                                log.info("Cliente actualizado exitosamente en la venta: {}", selectedCliente.getId());
                                // No necesitamos actualizar la UI aquí, ya que recibiremos una actualización a través de la suscripción
                            } else {
                                log.warn("Error al actualizar cliente: {}", response.message());
                                runOnUiThread(() -> alertUtil.showError("Error al actualizar cliente: " + response.message()));
                            }
                        },
                        error -> {
                            loadingIndicator.setVisible(false);
                            log.error("Error al actualizar cliente: {}", error.getMessage(), error);
                            runOnUiThread(() -> alertUtil.showError("Error al actualizar cliente: " + error.getMessage()));
                        }
                );
            }
        });

        // Manejar cuando el usuario presiona Enter sin seleccionar una sugerencia
        txtDocumentoNombreRazon.setOnAction(event -> {
            String inputText = txtDocumentoNombreRazon.getText().trim();
            if (!inputText.isEmpty()) {
                handleClienteInput(inputText, clienteMap);
            }
        });

        // Configurar menú contextual para eliminar cliente
        configureClienteContextMenu();

        log.debug("Autocompletado de clientes configurado");
    }

    /**
     * Maneja la entrada de texto del usuario para buscar o crear clientes.
     * @param inputText El texto ingresado por el usuario
     * @param clienteMap Mapa de clientes disponibles en el autocompletado
     */
    private void handleClienteInput(String inputText, java.util.Map<String, Cliente> clienteMap) {
        if (saleId == null) {
            log.warn("No se puede procesar cliente: saleId es null");
            return;
        }

        // Verificar si el texto coincide exactamente con alguna sugerencia existente
        Cliente existingCliente = clienteMap.get(inputText);
        if (existingCliente != null) {
            // Cliente encontrado en las sugerencias, usarlo directamente
            updateClienteInSale(existingCliente.getId());
            return;
        }

        // Buscar cliente en el servidor
        loadingIndicator.setVisible(true);

        // Determinar tipo de búsqueda basado en el contenido
        boolean isNumeric = inputText.matches("\\d+");

        reactor.core.publisher.Flux<Cliente> searchFlux;
        if (isNumeric) {
            log.debug("Buscando cliente por documento: {}", inputText);
            searchFlux = clienteService.searchClientesByDocument(inputText);
        } else {
            log.debug("Buscando cliente por nombre: {}", inputText);
            searchFlux = clienteService.searchClientesByName(inputText);
        }

        subscribeOnBoundedElastic(
                searchFlux.take(1).collectList(),
                clientes -> {
                    loadingIndicator.setVisible(false);
                    if (!clientes.isEmpty()) {
                        // Cliente encontrado, usar el primero
                        Cliente foundCliente = clientes.getFirst();
                        log.info("Cliente encontrado: {}", foundCliente.getId());
                        updateClienteInSale(foundCliente.getId());

                        // Actualizar el texto del campo con el formato correcto
                        runOnUiThread(() -> txtDocumentoNombreRazon.setText(formatClienteDisplay(foundCliente)));
                    } else {
                        // Cliente no encontrado, ofrecer crear uno nuevo
                        log.info("Cliente no encontrado, ofreciendo crear nuevo para: {}", inputText);
                        runOnUiThread(() -> offerToCreateNewCliente(inputText, isNumeric));
                    }
                },
                error -> {
                    loadingIndicator.setVisible(false);
                    log.error("Error al buscar cliente: {}", error.getMessage(), error);
                    runOnUiThread(() -> alertUtil.showError("Error al buscar cliente: " + error.getMessage()));
                }
        );
    }

    /**
     * Actualiza el cliente en la venta actual.
     * @param clienteId ID del cliente a asignar
     */
    private void updateClienteInSale(java.util.UUID clienteId) {
        loadingIndicator.setVisible(true);
        subscribeOnBoundedElastic(
                saleService.updateCliente(saleId, clienteId),
                response -> {
                    loadingIndicator.setVisible(false);
                    if (response.success()) {
                        log.info("Cliente actualizado exitosamente en la venta: {}", clienteId);
                    } else {
                        log.warn("Error al actualizar cliente: {}", response.message());
                        runOnUiThread(() -> alertUtil.showError("Error al actualizar cliente: " + response.message()));
                    }
                },
                error -> {
                    loadingIndicator.setVisible(false);
                    log.error("Error al actualizar cliente: {}", error.getMessage(), error);
                    runOnUiThread(() -> alertUtil.showError("Error al actualizar cliente: " + error.getMessage()));
                }
        );
    }

    /**
     * Ofrece al usuario crear un nuevo cliente cuando no se encuentra uno existente.
     * Utiliza el componente reutilizable ClienteCreationHelper.
     * @param inputText El texto ingresado por el usuario
     * @param isNumeric Si el texto es completamente numérico (documento)
     */
    private void offerToCreateNewCliente(String inputText, boolean isNumeric) {
        // Obtener el Stage actual para el diálogo modal
        javafx.stage.Stage currentStage = (javafx.stage.Stage) txtDocumentoNombreRazon.getScene().getWindow();

        // Usar el helper para mostrar el diálogo de creación de cliente
        clienteCreationHelper.offerToCreateCliente(
                inputText,
                isNumeric,
                (createdCliente, originalText) -> {
                    // Callback cuando se crea exitosamente el cliente
                    log.info("Cliente creado exitosamente desde diálogo: {}", createdCliente.getId());

                    // Asignar el nuevo cliente a la venta
                    updateClienteInSale(createdCliente.getId());

                    // Actualizar el texto del campo con el formato correcto
                    runOnUiThread(() -> {
                        txtDocumentoNombreRazon.setText(formatClienteDisplay(createdCliente));
                        alertUtil.showInfo("Cliente creado",
                            "Cliente creado exitosamente.\n" +
                            "Puede editar sus datos posteriormente si es necesario.");
                    });
                },
                currentStage
        );
    }



    /**
     * Formatea la información del cliente para mostrar en el autocompletado.
     */
    private String formatClienteDisplay(Cliente cliente) {
        if (cliente == null) return "";

        StringBuilder display = new StringBuilder();

        // Agregar documento
        if (cliente.getDni() != null && !cliente.getDni().isEmpty()) {
            display.append(cliente.getDni());
        } else if (cliente.getRuc() != null && !cliente.getRuc().isEmpty()) {
            display.append(cliente.getRuc());
        } else if (cliente.getOtroDocumento() != null && !cliente.getOtroDocumento().isEmpty()) {
            display.append(cliente.getOtroDocumento());
        }

        // Agregar nombre o razón social
        String nombre = "";
        if (cliente.getRazonSocial() != null && !cliente.getRazonSocial().isEmpty()) {
            nombre = cliente.getRazonSocial();
        } else if (cliente.getNombre() != null && !cliente.getNombre().isEmpty()) {
            nombre = cliente.getNombre();
            if (cliente.getApellido() != null && !cliente.getApellido().isEmpty()) {
                nombre += " " + cliente.getApellido();
            }
        }

        if (!nombre.isEmpty()) {
            if (!display.isEmpty()) {
                display.append(" - ");
            }
            display.append(nombre);
        }

        return display.toString();
    }

    /**
     * Configura el menú contextual para el campo de cliente.
     */
    private void configureClienteContextMenu() {
        javafx.scene.control.ContextMenu contextMenu = new javafx.scene.control.ContextMenu();

        // Opción para editar cliente actual
        javafx.scene.control.MenuItem editClienteItem = new javafx.scene.control.MenuItem("Editar Cliente");
        editClienteItem.setOnAction(event -> {
            if (currentSale != null && currentSale.getCliente() != null) {
                log.debug("Abriendo diálogo para editar cliente: {}", currentSale.getCliente().getId());

                // Obtener el Stage actual para el diálogo modal
                javafx.stage.Stage currentStage = (javafx.stage.Stage) txtDocumentoNombreRazon.getScene().getWindow();

                // Usar el helper para mostrar el diálogo de edición de cliente
                clienteCreationHelper.showEditClienteDialog(
                        currentSale.getCliente(),
                        (updatedCliente) -> {
                            // Callback cuando se actualiza exitosamente el cliente
                            log.info("Cliente actualizado exitosamente desde diálogo: {}", updatedCliente.getId());

                            // Actualizar el texto del campo con el formato correcto
                            runOnUiThread(() -> {
                                txtDocumentoNombreRazon.setText(formatClienteDisplay(updatedCliente));
                                alertUtil.showInfo("Cliente actualizado",
                                    "Los datos del cliente han sido actualizados exitosamente.");
                            });
                        },
                        currentStage
                );
            } else {
                alertUtil.showWarning("No hay cliente seleccionado para editar.");
            }
        });

        // Opción para eliminar cliente (venta genérica)
        javafx.scene.control.MenuItem removeClienteItem = new javafx.scene.control.MenuItem("Eliminar Cliente (Venta Genérica)");
        removeClienteItem.setOnAction(event -> {
            if (saleId != null) {
                log.debug("Eliminando cliente de la venta: {}", saleId);

                loadingIndicator.setVisible(true);
                subscribeOnBoundedElastic(
                        saleService.updateCliente(saleId, null), // null para venta genérica
                        response -> {
                            loadingIndicator.setVisible(false);
                            if (response.success()) {
                                log.info("Cliente eliminado exitosamente de la venta: {}", saleId);
                                // No necesitamos actualizar la UI aquí, ya que recibiremos una actualización a través de la suscripción
                            } else {
                                log.warn("Error al eliminar cliente: {}", response.message());
                                runOnUiThread(() -> alertUtil.showError("Error al eliminar cliente: " + response.message()));
                            }
                        },
                        error -> {
                            loadingIndicator.setVisible(false);
                            log.error("Error al eliminar cliente: {}", error.getMessage(), error);
                            runOnUiThread(() -> alertUtil.showError("Error al eliminar cliente: " + error.getMessage()));
                        }
                );
            }
        });

        contextMenu.getItems().addAll(editClienteItem, removeClienteItem);
        txtDocumentoNombreRazon.setContextMenu(contextMenu);
    }

    /**
     * Asegura que el indicador de carga esté oculto y en el estado correcto.
     */
    private void ensureLoadingIndicatorHidden() {
        if (loadingIndicator != null) {
            Platform.runLater(() -> {
                loadingIndicator.setVisible(false);
                loadingIndicator.setProgress(-1.0); // Modo indeterminado
                log.debug("Indicador de carga inicializado y oculto");
            });
        }
    }

    /**
     * Establece el callback para notificar cuando se debe cerrar el SaleGui.
     */
    public void setOnSaleGuiCloseRequested(Runnable callback) {
        this.onSaleGuiCloseRequested = callback;
    }

    /**
     * Cierra el SaleGui actual notificando al controlador padre.
     */
    private void closeSaleGui() {
        log.info("Solicitando cierre del SaleGui para saleId: {}", saleId);
        if (onSaleGuiCloseRequested != null) {
            onSaleGuiCloseRequested.run();
        } else {
            log.warn("No hay callback configurado para cerrar el SaleGui");
        }
    }


}
